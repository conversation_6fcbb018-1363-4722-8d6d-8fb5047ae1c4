# PopupBanner API Documentation

## 🎯 Overview
The PopupBanner API provides a comprehensive CRUD system for managing popup banners with multi-level approval workflow. The system supports different content types and role-based access control.

## 🔐 Authentication
All endpoints (except public API) require JWT authentication:
```
Authorization: Bearer <your_jwt_token>
```

## 👥 User Roles & Permissions

### **Contributor**
- ✅ Create popup banners
- ✅ View/edit own banners
- ❌ Cannot approve/reject banners
- ❌ Cannot activate banners

### **Customer Care**
- ✅ View all banners
- ✅ Approve/reject banners
- ✅ Activate/deactivate approved banners
- ❌ Cannot create banners
- ❌ Cannot edit banner content

### **Admin**
- ✅ Full control over all banners
- ✅ Override any approval decision
- ✅ Edit any banner content
- ✅ Delete banners

### **Public**
- ✅ View only active, approved banners
- ❌ No authentication required

## 📋 Content Types

| Type | Description | Required Fields |
|------|-------------|----------------|
| `image_only` | Image only | `image` |
| `text_image` | Text + Image | `text_content`, `image` |
| `image_text` | Image + Text | `image`, `text_content` |
| `text_link` | Text + Link | `text_content`, `link_url`, `link_text` |
| `link_anchor` | Link + Anchor | `link_url`, `link_text`, `anchor_tag` |

## 🔄 Approval Workflow

```
[Contributor Creates] → [Pending] 
                           ↓
[Customer Care Reviews] → [Approved by Care] OR [Rejected by Care]
                           ↓
[Admin Reviews] → [Approved by Admin] OR [Rejected by Admin]
                           ↓
[Activation] → [Active] (visible to public)
```

## 🌐 Base URLs

- **Contributor**: `/api/contributor/popup-banners/`
- **Customer Care**: `/api/customrcare/popup-banners/`
- **Admin**: `/api/customrcare/admin/popup-banners/`
- **Public**: `/api/popup-banners/`

---

## 📚 API Endpoints

### 1. Contributor Endpoints

#### **Create Banner**
```bash
POST /api/contributor/popup-banners/
```

**Example - Text Only Banner:**
```bash
curl -X POST "http://localhost:8000/api/contributor/popup-banners/" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Welcome Banner",
    "description": "Welcome message for new users",
    "content_type": "text_only",
    "text_content": "Welcome to our platform!",
    "priority": "high",
    "display_duration": 5000
  }'
```

**Example - Image + Text Banner:**
```bash
curl -X POST "http://localhost:8000/api/contributor/popup-banners/" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "title=Product Launch" \
  -F "description=New product announcement" \
  -F "content_type=text_image" \
  -F "text_content=Check out our new product!" \
  -F "image=@/path/to/image.jpg" \
  -F "priority=medium"
```

**Example - Text + Link Banner:**
```bash
curl -X POST "http://localhost:8000/api/contributor/popup-banners/" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Special Offer",
    "content_type": "text_link",
    "text_content": "Get 50% off on all courses!",
    "link_url": "https://example.com/offers",
    "link_text": "Claim Now",
    "priority": "urgent"
  }'
```

#### **List Own Banners**
```bash
GET /api/contributor/popup-banners/

curl -X GET "http://localhost:8000/api/contributor/popup-banners/" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

#### **Get Banner Details**
```bash
GET /api/contributor/popup-banners/{id}/

curl -X GET "http://localhost:8000/api/contributor/popup-banners/1/" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

#### **Update Banner**
```bash
PUT /api/contributor/popup-banners/{id}/

curl -X PUT "http://localhost:8000/api/contributor/popup-banners/1/" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Updated Banner Title",
    "description": "Updated description",
    "priority": "low"
  }'
```

#### **Delete Banner**
```bash
DELETE /api/contributor/popup-banners/{id}/

curl -X DELETE "http://localhost:8000/api/contributor/popup-banners/1/" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

#### **Get Statistics**
```bash
GET /api/contributor/popup-banners/stats/

curl -X GET "http://localhost:8000/api/contributor/popup-banners/stats/" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

### 2. Customer Care Endpoints

#### **List All Banners**
```bash
GET /api/customrcare/popup-banners/

# List all banners
curl -X GET "http://localhost:8000/api/customrcare/popup-banners/" \
  -H "Authorization: Bearer YOUR_CARE_TOKEN"

# Filter by approval status
curl -X GET "http://localhost:8000/api/customrcare/popup-banners/?approval_status=pending" \
  -H "Authorization: Bearer YOUR_CARE_TOKEN"

# Filter by content type
curl -X GET "http://localhost:8000/api/customrcare/popup-banners/?content_type=text_image" \
  -H "Authorization: Bearer YOUR_CARE_TOKEN"

# Filter by creator
curl -X GET "http://localhost:8000/api/customrcare/popup-banners/?created_by=contributor1" \
  -H "Authorization: Bearer YOUR_CARE_TOKEN"
```

#### **Approve Banner**
```bash
PATCH /api/customrcare/popup-banners/{id}/

curl -X PATCH "http://localhost:8000/api/customrcare/popup-banners/1/" \
  -H "Authorization: Bearer YOUR_CARE_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "approval_status": "approved_by_care"
  }'
```

#### **Reject Banner**
```bash
PATCH /api/customrcare/popup-banners/{id}/

curl -X PATCH "http://localhost:8000/api/customrcare/popup-banners/1/" \
  -H "Authorization: Bearer YOUR_CARE_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "approval_status": "rejected_by_care",
    "rejection_reason": "Content does not meet guidelines"
  }'
```

#### **Activate Banner**
```bash
PATCH /api/customrcare/popup-banners/{id}/

curl -X PATCH "http://localhost:8000/api/customrcare/popup-banners/1/" \
  -H "Authorization: Bearer YOUR_CARE_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "is_active": true
  }'
```

---

### 3. Admin Endpoints

#### **List All Banners (Admin)**
```bash
GET /api/customrcare/admin/popup-banners/

# List all banners
curl -X GET "http://localhost:8000/api/customrcare/admin/popup-banners/" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"

# Filter by active status
curl -X GET "http://localhost:8000/api/customrcare/admin/popup-banners/?is_active=true" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"
```

#### **Admin Approve Banner**
```bash
PATCH /api/customrcare/admin/popup-banners/{id}/

curl -X PATCH "http://localhost:8000/api/customrcare/admin/popup-banners/1/" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "approval_status": "approved_by_admin"
  }'
```

#### **Admin Edit Banner Content**
```bash
PUT /api/customrcare/admin/popup-banners/{id}/

curl -X PUT "http://localhost:8000/api/customrcare/admin/popup-banners/1/" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Admin Updated Title",
    "text_content": "Admin updated content",
    "priority": "urgent",
    "is_active": true
  }'
```

#### **Delete Banner (Admin)**
```bash
DELETE /api/customrcare/admin/popup-banners/{id}/

curl -X DELETE "http://localhost:8000/api/customrcare/admin/popup-banners/1/" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"
```

---

### 4. Public Endpoints

#### **Get Active Banners**
```bash
GET /api/popup-banners/

# No authentication required
curl -X GET "http://localhost:8000/api/popup-banners/"
```

**Response Example:**
```json
[
  {
    "id": 1,
    "title": "Welcome Banner",
    "description": "Welcome message",
    "content_type": "text_image",
    "content_type_display": "Text + Image",
    "image": "http://localhost:8000/media/popup_banners/images/banner1.jpg",
    "text_content": "Welcome to our platform!",
    "link_url": null,
    "link_text": null,
    "anchor_tag": null,
    "display_duration": 5000,
    "priority": "high",
    "priority_display": "High",
    "created_at": "2024-01-15T10:30:00Z"
  }
]
```

---

## 📊 Response Examples

### **Banner Object Structure**
```json
{
  "id": 1,
  "title": "Sample Banner",
  "description": "Sample description",
  "content_type": "text_image",
  "content_type_display": "Text + Image",
  "image": "http://localhost:8000/media/popup_banners/images/sample.jpg",
  "text_content": "Sample text content",
  "link_url": "https://example.com",
  "link_text": "Click Here",
  "anchor_tag": "target='_blank'",
  "display_duration": 5000,
  "priority": "medium",
  "priority_display": "Medium",
  "is_active": true,
  "approval_status": "approved_by_care",
  "approval_status_display": "Approved by Customer Care",
  "rejection_reason": null,
  "created_by": 1,
  "created_by_username": "contributor1",
  "approved_by_care": 1,
  "approved_by_care_username": "care1",
  "approved_by_admin": null,
  "approved_by_admin_username": null,
  "rejected_by": null,
  "rejected_by_username": null,
  "created_at": "2024-01-15T10:30:00Z",
  "updated_at": "2024-01-15T11:00:00Z",
  "approved_at": "2024-01-15T10:45:00Z",
  "activated_at": "2024-01-15T11:00:00Z",
  "slug": "sample-banner"
}
```

### **Statistics Response**
```json
{
  "role": "contributor",
  "total_banners": 5,
  "active_banners": 2,
  "pending_approval": 2,
  "approved_by_care": 2,
  "approved_by_admin": 1,
  "rejected_by_care": 1,
  "rejected_by_admin": 0,
  "content_types": {
    "image_only": 1,
    "text_image": 2,
    "image_text": 1,
    "text_link": 1,
    "link_anchor": 0
  },
  "priorities": {
    "low": 1,
    "medium": 2,
    "high": 1,
    "urgent": 1
  }
}
```

---

## 🚨 Error Responses

### **Validation Errors**
```json
{
  "image": ["Image is required for content type 'Image Only'"],
  "link_url": ["Link URL is required for content type 'Text + Link'"]
}
```

### **Permission Errors**
```json
{
  "detail": "You do not have permission for this resource."
}
```

### **Not Found Errors**
```json
{
  "detail": "Not found."
}
```

### **Approval Workflow Errors**
```json
{
  "non_field_errors": ["Banner cannot be approved at this stage"]
}
```

---

## 🧪 Testing Workflow

### **Complete Workflow Test**

1. **Contributor Creates Banner:**
```bash
curl -X POST "http://localhost:8000/api/contributor/popup-banners/" \
  -H "Authorization: Bearer CONTRIBUTOR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Test Workflow Banner",
    "content_type": "text_only",
    "text_content": "Testing the complete workflow",
    "priority": "medium"
  }'
```

2. **Customer Care Approves:**
```bash
curl -X PATCH "http://localhost:8000/api/customrcare/popup-banners/1/" \
  -H "Authorization: Bearer CARE_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "approval_status": "approved_by_care"
  }'
```

3. **Customer Care Activates:**
```bash
curl -X PATCH "http://localhost:8000/api/customrcare/popup-banners/1/" \
  -H "Authorization: Bearer CARE_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "is_active": true
  }'
```

4. **Public Can See Active Banner:**
```bash
curl -X GET "http://localhost:8000/api/popup-banners/"
```

### **Admin Override Test**

1. **Admin Directly Approves Pending Banner:**
```bash
curl -X PATCH "http://localhost:8000/api/customrcare/admin/popup-banners/2/" \
  -H "Authorization: Bearer ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "approval_status": "approved_by_admin",
    "is_active": true
  }'
```

### **Rejection Test**

1. **Customer Care Rejects Banner:**
```bash
curl -X PATCH "http://localhost:8000/api/customrcare/popup-banners/3/" \
  -H "Authorization: Bearer CARE_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "approval_status": "rejected_by_care",
    "rejection_reason": "Content violates community guidelines"
  }'
```

---

## 📱 Frontend Integration

### **JavaScript Example - Fetch Active Banners**
```javascript
async function fetchActivePopupBanners() {
  try {
    const response = await fetch('/api/popup-banners/');
    const banners = await response.json();

    banners.forEach(banner => {
      displayPopupBanner(banner);
    });
  } catch (error) {
    console.error('Error fetching banners:', error);
  }
}

function displayPopupBanner(banner) {
  const popup = document.createElement('div');
  popup.className = 'popup-banner';
  popup.style.display = 'none';

  let content = '';

  switch (banner.content_type) {
    case 'image_only':
      content = `<img src="${banner.image}" alt="${banner.title}">`;
      break;
    case 'text_image':
      content = `
        <div class="banner-text">${banner.text_content}</div>
        <img src="${banner.image}" alt="${banner.title}">
      `;
      break;
    case 'text_link':
      content = `
        <div class="banner-text">${banner.text_content}</div>
        <a href="${banner.link_url}" ${banner.anchor_tag || ''}>${banner.link_text}</a>
      `;
      break;
  }

  popup.innerHTML = `
    <div class="banner-content">
      <h3>${banner.title}</h3>
      ${content}
      <button onclick="closeBanner(this)">×</button>
    </div>
  `;

  document.body.appendChild(popup);

  // Show banner
  popup.style.display = 'block';

  // Auto-hide after display_duration
  setTimeout(() => {
    popup.style.display = 'none';
  }, banner.display_duration);
}

function closeBanner(button) {
  button.closest('.popup-banner').style.display = 'none';
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', fetchActivePopupBanners);
```

### **CSS Example - Banner Styling**
```css
.popup-banner {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  z-index: 10000;
  max-width: 500px;
  padding: 20px;
}

.banner-content {
  position: relative;
}

.banner-content h3 {
  margin-top: 0;
  color: #333;
}

.banner-content img {
  max-width: 100%;
  height: auto;
  border-radius: 4px;
}

.banner-content button {
  position: absolute;
  top: -10px;
  right: -10px;
  background: #ff4444;
  color: white;
  border: none;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  cursor: pointer;
}
```

---

## 🔧 Configuration

### **Django Settings**
```python
# settings.py

# Media files configuration for banner images
MEDIA_URL = '/media/'
MEDIA_ROOT = os.path.join(BASE_DIR, 'media')

# Add to INSTALLED_APPS
INSTALLED_APPS = [
    # ... other apps
    'contributor',
    'customrcare',
]

# Add to URL configuration
# urls.py
from django.conf import settings
from django.conf.urls.static import static

if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
```

---

## 📋 Summary

The PopupBanner API provides:

✅ **Complete CRUD Operations** with role-based access
✅ **Multi-level Approval Workflow** (Contributor → Customer Care → Admin)
✅ **5 Content Types** with validation
✅ **Public API** for frontend integration
✅ **Comprehensive Permissions** and security
✅ **Statistics and Analytics** endpoints
✅ **Django Admin Interface** for management
✅ **Extensive Testing** and documentation

The system ensures data isolation, proper approval workflows, and secure access control while providing flexibility for different content types and use cases.

### **About public_urls.py**

The `public_urls.py` file serves these important purposes:

1. **Separation of Concerns**: Keeps public APIs separate from authenticated APIs
2. **Security**: Only exposes active, approved banners without sensitive data
3. **Frontend Integration**: Provides a clean endpoint for websites to fetch banners
4. **No Authentication Required**: Public access for displaying banners to visitors
5. **Clean URL Structure**: `/api/popup-banners/` for public vs `/api/contributor/popup-banners/` for contributors

**Example Usage:**
- Website visitors see banners via `/api/popup-banners/` (no login needed)
- Contributors manage banners via `/api/contributor/popup-banners/` (login required)
- Customer care approves via `/api/customrcare/popup-banners/` (login required)
