from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from .models import ContributorProfile, ContributorPoints, Banner, PopupBanner

# Register your models here.


@admin.register(PopupBanner)
class PopupBannerAdmin(admin.ModelAdmin):
    """
    Admin interface for PopupBanner with comprehensive management features
    """
    list_display = [
        'title', 'content_type', 'approval_status', 'is_active',
        'priority', 'created_by', 'created_at', 'image_preview'
    ]
    list_filter = [
        'approval_status', 'content_type', 'priority', 'is_active',
        'created_at', 'updated_at'
    ]
    search_fields = [
        'title', 'description', 'text_content', 'link_text',
        'created_by__username', 'created_by__email'
    ]
    readonly_fields = [
        'slug', 'created_at', 'updated_at', 'approved_at', 'activated_at',
        'image_preview', 'approval_workflow_status'
    ]
    fieldsets = (
        ('Basic Information', {
            'fields': ('title', 'description', 'content_type', 'slug')
        }),
        ('Content', {
            'fields': ('image', 'image_preview', 'text_content', 'link_url', 'link_text', 'anchor_tag'),
            'classes': ('collapse',)
        }),
        ('Display Settings', {
            'fields': ('display_duration', 'priority', 'is_active')
        }),
        ('Approval Workflow', {
            'fields': (
                'approval_status', 'approval_workflow_status', 'rejection_reason',
                'created_by', 'approved_by_care', 'approved_by_admin', 'rejected_by'
            ),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at', 'approved_at', 'activated_at'),
            'classes': ('collapse',)
        }),
    )

    # Ordering
    ordering = ['-created_at']

    # Items per page
    list_per_page = 25

    # Actions
    actions = ['approve_by_admin', 'reject_banners', 'activate_banners', 'deactivate_banners']

    def image_preview(self, obj):
        """Display image preview in admin"""
        if obj.image:
            return format_html(
                '<img src="{}" style="max-width: 100px; max-height: 100px;" />',
                obj.image.url
            )
        return "No image"
    image_preview.short_description = "Image Preview"

    def approval_workflow_status(self, obj):
        """Display approval workflow status with colors"""
        status_colors = {
            'pending': '#ffc107',
            'approved_by_care': '#17a2b8',
            'approved_by_admin': '#28a745',
            'rejected_by_care': '#dc3545',
            'rejected_by_admin': '#dc3545',
            'active': '#28a745',
            'inactive': '#6c757d',
        }
        color = status_colors.get(obj.approval_status, '#6c757d')
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color,
            obj.get_approval_status_display()
        )
    approval_workflow_status.short_description = "Workflow Status"

    def approve_by_admin(self, request, queryset):
        """Bulk action to approve banners by admin"""
        count = 0
        for banner in queryset:
            if banner.approve_by_admin(request.user):
                banner.save()
                count += 1

        self.message_user(
            request,
            f'{count} banner(s) were successfully approved by admin.'
        )
    approve_by_admin.short_description = "Approve selected banners (Admin)"

    def reject_banners(self, request, queryset):
        """Bulk action to reject banners"""
        count = 0
        for banner in queryset:
            if banner.reject(request.user, "Bulk rejection by admin"):
                banner.save()
                count += 1

        self.message_user(
            request,
            f'{count} banner(s) were successfully rejected.'
        )
    reject_banners.short_description = "Reject selected banners"

    def activate_banners(self, request, queryset):
        """Bulk action to activate banners"""
        count = 0
        for banner in queryset:
            if banner.activate():
                banner.save()
                count += 1

        self.message_user(
            request,
            f'{count} banner(s) were successfully activated.'
        )
    activate_banners.short_description = "Activate selected banners"

    def deactivate_banners(self, request, queryset):
        """Bulk action to deactivate banners"""
        count = 0
        for banner in queryset:
            if banner.deactivate():
                banner.save()
                count += 1

        self.message_user(
            request,
            f'{count} banner(s) were successfully deactivated.'
        )
    deactivate_banners.short_description = "Deactivate selected banners"

    def get_queryset(self, request):
        """Optimize queryset with select_related"""
        return super().get_queryset(request).select_related(
            'created_by', 'approved_by_care__user', 'approved_by_admin', 'rejected_by'
        )


admin.site.register(ContributorProfile)
admin.site.register(ContributorPoints)
admin.site.register(Banner)
