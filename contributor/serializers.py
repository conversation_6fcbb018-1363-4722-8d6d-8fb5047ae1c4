from rest_framework import serializers
from django.contrib.auth.models import User
from .models import Contributor<PERSON><PERSON><PERSON><PERSON>, <PERSON>, PopupBanner
from django.contrib.auth import authenticate
from shashtrarth.utils import validate_username, validate_password


# class UserSerializer(serializers.ModelSerializer):
#     class Meta:
#         model = User
#         fields = [
#             "id",
#             "username",
#             "email",
#             "first_name",
#             "last_name",
#             "password",
#         ]
#         extra_kwargs = {"password": {"write_only": True}}


class ContributorProfileSerializer(serializers.ModelSerializer):
    class Meta:
        model = ContributorProfile
        fields = ["security_question", "security_answer"]


class ContributorRegistrationSerializer(serializers.ModelSerializer):
    contributor_profile = ContributorProfileSerializer()

    class Meta:
        model = User
        fields = [
            "id",
            "username",
            "email",
            "password",
            "first_name",
            "last_name",
            "contributor_profile",
        ]
        extra_kwargs = {"password": {"write_only": True}}

    def validate_username(self, value):
        """Validate username using centralized validation utility"""
        return validate_username(value)

    def validate_password(self, value):
        """Validate password using centralized validation utility"""
        return validate_password(value)

    def create(self, validated_data):
        profile_data = validated_data.pop("contributor_profile")
        user = User.objects.create_user(**validated_data)
        ContributorProfile.objects.create(user=user, **profile_data)
        return user

    def update(self, instance, validated_data):
        profile_data = validated_data.pop("contributor_profile", None)

        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()

        if profile_data:
            profile = instance.contributorprofile
            for attr, value in profile_data.items():
                setattr(profile, attr, value)
            profile.save()

        return instance


class LoginSerializer(serializers.Serializer):
    username = serializers.CharField(required=True)
    password = serializers.CharField(required=True, write_only=True)

    def validate(self, data):
        username = data.get("username")
        password = data.get("password")

        if username and password:
            user = authenticate(username=username, password=password)
            if user:
                if not user.is_active:
                    raise serializers.ValidationError("User account is disabled.")

                # Check if user has a contributor profile
                try:
                    contributor_profile = user.contributor_profile
                except ContributorProfile.DoesNotExist:
                    raise serializers.ValidationError(
                        "Access denied. This account is not authorized for contributor access."
                    )

                # Verify the role is correct
                if contributor_profile.role != "contributor":
                    raise serializers.ValidationError(
                        "Access denied. Invalid role for contributor access."
                    )

                return {"user": user, "role": contributor_profile.role}
            else:
                raise serializers.ValidationError(
                    "Unable to log in with provided credentials."
                )
        else:
            raise serializers.ValidationError("Must include 'username' and 'password'.")

class BannerSerializer(serializers.ModelSerializer):
    class Meta:
        model = Banner
        fields = '__all__'


class PopupBannerBaseSerializer(serializers.ModelSerializer):
    """Base serializer for PopupBanner with common fields"""
    created_by_username = serializers.CharField(source='created_by.username', read_only=True)
    approved_by_care_username = serializers.CharField(source='approved_by_care.user.username', read_only=True)
    approved_by_admin_username = serializers.CharField(source='approved_by_admin.username', read_only=True)
    rejected_by_username = serializers.CharField(source='rejected_by.username', read_only=True)
    content_type_display = serializers.CharField(source='get_content_type_display', read_only=True)
    approval_status_display = serializers.CharField(source='get_approval_status_display', read_only=True)
    priority_display = serializers.CharField(source='get_priority_display', read_only=True)

    class Meta:
        model = PopupBanner
        fields = [
            'id', 'title', 'description', 'content_type', 'content_type_display',
            'image', 'text_content', 'link_url', 'link_text', 'anchor_tag',
            'display_duration', 'priority', 'priority_display', 'is_active',
            'approval_status', 'approval_status_display', 'rejection_reason',
            'created_by', 'created_by_username', 'approved_by_care', 'approved_by_care_username',
            'approved_by_admin', 'approved_by_admin_username', 'rejected_by', 'rejected_by_username',
            'created_at', 'updated_at', 'approved_at', 'activated_at', 'slug'
        ]
        read_only_fields = [
            'id', 'created_by', 'approved_by_care', 'approved_by_admin', 'rejected_by',
            'created_at', 'updated_at', 'approved_at', 'activated_at', 'slug'
        ]


class PopupBannerContributorSerializer(PopupBannerBaseSerializer):
    """Serializer for contributors - can create and view own banners"""

    class Meta(PopupBannerBaseSerializer.Meta):
        # Contributors cannot modify approval-related fields
        read_only_fields = PopupBannerBaseSerializer.Meta.read_only_fields + [
            'approval_status', 'rejection_reason', 'is_active'
        ]

    def validate(self, data):
        """Validate content based on content type"""
        content_type = data.get('content_type')

        if content_type in ['image_only', 'text_image', 'image_text']:
            if not data.get('image'):
                raise serializers.ValidationError({
                    'image': f"Image is required for content type '{content_type}'"
                })

        if content_type in ['text_image', 'image_text', 'text_link']:
            if not data.get('text_content'):
                raise serializers.ValidationError({
                    'text_content': f"Text content is required for content type '{content_type}'"
                })

        if content_type in ['text_link', 'link_anchor']:
            if not data.get('link_url'):
                raise serializers.ValidationError({
                    'link_url': f"Link URL is required for content type '{content_type}'"
                })
            if not data.get('link_text'):
                raise serializers.ValidationError({
                    'link_text': f"Link text is required for content type '{content_type}'"
                })

        if content_type == 'link_anchor':
            if not data.get('anchor_tag'):
                raise serializers.ValidationError({
                    'anchor_tag': "Anchor tag is required for 'Link + Anchor Tag' content type"
                })

        return data

    def create(self, validated_data):
        """Set the created_by field to the current user"""
        validated_data['created_by'] = self.context['request'].user
        return super().create(validated_data)


class PopupBannerCustomerCareSerializer(PopupBannerBaseSerializer):
    """Serializer for customer care - can approve/reject banners"""

    class Meta(PopupBannerBaseSerializer.Meta):
        # Customer care can modify approval status and rejection reason
        read_only_fields = [
            'id', 'created_by', 'approved_by_admin', 'rejected_by',
            'created_at', 'updated_at', 'approved_at', 'activated_at', 'slug',
            'title', 'description', 'content_type', 'image', 'text_content',
            'link_url', 'link_text', 'anchor_tag', 'display_duration', 'priority'
        ]

    def update(self, instance, validated_data):
        """Handle approval/rejection logic"""
        approval_status = validated_data.get('approval_status')
        rejection_reason = validated_data.get('rejection_reason')

        if approval_status == 'approved_by_care':
            if instance.can_be_approved_by_care():
                care_profile = self.context['request'].user.customrcare_profile
                instance.approve_by_care(care_profile)
            else:
                raise serializers.ValidationError("Banner cannot be approved at this stage")

        elif approval_status in ['rejected_by_care']:
            if not rejection_reason:
                raise serializers.ValidationError("Rejection reason is required")
            instance.reject(self.context['request'].user, rejection_reason)

        # Handle activation/deactivation
        is_active = validated_data.get('is_active')
        if is_active is not None:
            if is_active:
                if not instance.activate():
                    raise serializers.ValidationError("Banner cannot be activated")
            else:
                instance.deactivate()

        instance.save()
        return instance


class PopupBannerAdminSerializer(PopupBannerBaseSerializer):
    """Serializer for admin - full control over banners"""

    class Meta(PopupBannerBaseSerializer.Meta):
        # Admin can modify most fields except timestamps and slug
        read_only_fields = [
            'id', 'created_by', 'created_at', 'updated_at', 'approved_at', 'activated_at', 'slug'
        ]

    def update(self, instance, validated_data):
        """Handle admin approval/rejection logic"""
        approval_status = validated_data.get('approval_status')
        rejection_reason = validated_data.get('rejection_reason')

        if approval_status == 'approved_by_admin':
            if instance.can_be_approved_by_admin():
                instance.approve_by_admin(self.context['request'].user)
            else:
                raise serializers.ValidationError("Banner cannot be approved at this stage")

        elif approval_status in ['rejected_by_admin']:
            if not rejection_reason:
                raise serializers.ValidationError("Rejection reason is required")
            instance.reject(self.context['request'].user, rejection_reason)

        # Handle activation/deactivation
        is_active = validated_data.get('is_active')
        if is_active is not None:
            if is_active:
                if not instance.activate():
                    raise serializers.ValidationError("Banner cannot be activated")
            else:
                instance.deactivate()

        # Update other fields
        for attr, value in validated_data.items():
            if attr not in ['approval_status', 'is_active']:
                setattr(instance, attr, value)

        instance.save()
        return instance


class PopupBannerPublicSerializer(serializers.ModelSerializer):
    """Serializer for public API - only active banners with minimal fields"""
    content_type_display = serializers.CharField(source='get_content_type_display', read_only=True)
    priority_display = serializers.CharField(source='get_priority_display', read_only=True)

    class Meta:
        model = PopupBanner
        fields = [
            'id', 'title', 'description', 'content_type', 'content_type_display',
            'image', 'text_content', 'link_url', 'link_text', 'anchor_tag',
            'display_duration', 'priority', 'priority_display', 'created_at'
        ]
        read_only_fields = '__all__'